<div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
  data-sidebar-position="fixed" data-header-position="fixed">
  
  <!-- Sidebar Start -->
  <aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div>
      <div class="login-header">
        <img src="assets/images/logos/esprit.png" alt="" style="width: 200px; height: auto; display: block; margin-left: 0px;" />
      </div>
      <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
        <i class="ti ti-x fs-8"></i>
      </div>
    </div>
    <!-- End Sidebar scroll-->
  </aside>
  <!--  Sidebar End -->

  <!--  Main wrapper -->
  <div class="body-wrapper">
    <!--  Header Start -->
    <header class="app-header">
      <nav class="navbar navbar-expand-lg navbar-light">
        <ul class="navbar-nav">
          <li class="nav-item d-block d-xl-none">
            <a class="nav-link sidebartoggler nav-icon-hover" id="headerCollapse" href="javascript:void(0)">
              <i class="ti ti-menu-2"></i>
            </a>
          </li>
        </ul>
        <div class="navbar-collapse justify-content-end px-0" id="navbarNav">
          <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-end">
            <li class="nav-item dropdown">
              <a class="nav-link nav-icon-hover" href="javascript:void(0)" id="drop2" data-bs-toggle="dropdown"
                aria-expanded="false">
                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                  <i class="ti ti-user text-white"></i>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                <div class="message-body">
                  <a href="javascript:void(0)" class="d-flex align-items-center gap-2 dropdown-item">
                    <i class="ti ti-user fs-6"></i>
                    <p class="mb-0 fs-3">{{currentUser?.username}}</p>
                  </a>
                  <a href="javascript:void(0)" (click)="goBack()" class="btn btn-outline-primary mx-3 mt-2 d-block">Retour au Dashboard</a>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>
    <!--  Header End -->

    <div class="container-fluid">
      <!-- Profile Header -->
      <div class="welcome-header">
        <h1>Mon Profil</h1>
        <p>Gérez vos informations personnelles et professionnelles</p>
      </div>

      <!-- Success/Error Messages -->
      <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="ti ti-check-circle me-2"></i>
        {{ successMessage }}
      </div>

      <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="ti ti-alert-circle me-2"></i>
        {{ errorMessage }}
      </div>

      <!-- Profile Card -->
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <div class="card">
            <div class="card-header bg-primary text-white">
              <h5 class="card-title mb-0">
                <i class="ti ti-user-circle me-2"></i>
                Informations du Profil
              </h5>
            </div>
            <div class="card-body">
              <!-- Loading Spinner -->
              <div *ngIf="isLoading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Chargement...</span>
                </div>
              </div>

              <!-- Profile Form -->
              <form *ngIf="!isLoading" (ngSubmit)="saveProfile()" #profileFormRef="ngForm">
                <div class="row">
                  <!-- Username -->
                  <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">
                      <i class="ti ti-user me-1"></i>
                      Nom d'utilisateur
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="username"
                      name="username"
                      [(ngModel)]="profileData.username"
                      [readonly]="!isEditing"
                      required
                      #usernameField="ngModel">
                    <div *ngIf="usernameField.invalid && usernameField.touched" class="text-danger small">
                      Le nom d'utilisateur est requis.
                    </div>
                  </div>

                  <!-- Email -->
                  <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">
                      <i class="ti ti-mail me-1"></i>
                      Email
                    </label>
                    <input 
                      type="email" 
                      class="form-control" 
                      id="email"
                      name="email"
                      [(ngModel)]="profileData.email"
                      readonly
                      required>
                    <small class="text-muted">L'email ne peut pas être modifié</small>
                  </div>

                  <!-- Role -->
                  <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">
                      <i class="ti ti-shield me-1"></i>
                      Rôle
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="role"
                      [value]="currentUser?.role"
                      readonly>
                    <small class="text-muted">Le rôle ne peut pas être modifié</small>
                  </div>

                  <!-- Poste -->
                  <div class="col-md-6 mb-3">
                    <label for="poste" class="form-label">
                      <i class="ti ti-briefcase me-1"></i>
                      Poste
                    </label>
                    <select 
                      class="form-select" 
                      id="poste"
                      name="poste"
                      [(ngModel)]="profileData.poste"
                      [disabled]="!isEditing"
                      required
                      #posteField="ngModel">
                      <option value="">Sélectionnez un poste</option>
                      <option *ngFor="let poste of postes" [value]="poste">{{ poste }}</option>
                    </select>
                    <div *ngIf="posteField.invalid && posteField.touched" class="text-danger small">
                      Le poste est requis.
                    </div>
                  </div>

                  <!-- Secteur -->
                  <div class="col-md-6 mb-3">
                    <label for="secteur" class="form-label">
                      <i class="ti ti-building me-1"></i>
                      Secteur
                    </label>
                    <select 
                      class="form-select" 
                      id="secteur"
                      name="secteur"
                      [(ngModel)]="profileData.secteur"
                      [disabled]="!isEditing"
                      required
                      #secteurField="ngModel">
                      <option value="">Sélectionnez un secteur</option>
                      <option *ngFor="let secteur of secteurs" [value]="secteur">{{ secteur }}</option>
                    </select>
                    <div *ngIf="secteurField.invalid && secteurField.touched" class="text-danger small">
                      Le secteur est requis.
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-secondary" (click)="goBack()">
                    <i class="ti ti-arrow-left me-1"></i>
                    Retour
                  </button>
                  
                  <div>
                    <button 
                      *ngIf="!isEditing" 
                      type="button" 
                      class="btn btn-primary me-2" 
                      (click)="toggleEdit()">
                      <i class="ti ti-edit me-1"></i>
                      Modifier
                    </button>
                    
                    <button 
                      *ngIf="isEditing" 
                      type="button" 
                      class="btn btn-secondary me-2" 
                      (click)="toggleEdit()">
                      <i class="ti ti-x me-1"></i>
                      Annuler
                    </button>
                    
                    <button
                      *ngIf="isEditing"
                      type="submit"
                      class="btn btn-success"
                      [disabled]="!profileFormRef.valid || isLoading">
                      <i class="ti ti-check me-1"></i>
                      <span *ngIf="!isLoading">Sauvegarder</span>
                      <span *ngIf="isLoading">Sauvegarde...</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

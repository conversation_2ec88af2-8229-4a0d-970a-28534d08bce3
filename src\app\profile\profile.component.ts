import { Component, OnInit } from '@angular/core';
import { AuthService, User } from '../services/auth.service';
import { UtilisateurService } from '../utilisateur/utilisateur.service';
import { Router } from '@angular/router';

export interface ProfileUpdateRequest {
  username: string;
  poste: string;
  secteur: string;
  email: string;
}

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  standalone: false
})
export class ProfileComponent implements OnInit {
  currentUser: User | null = null;
  profileData: ProfileUpdateRequest = {
    username: '',
    poste: '',
    secteur: '',
    email: ''
  };

  passwordData: PasswordChangeRequest = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };

  isEditing = false;
  isChangingPassword = false;
  isLoading = false;
  successMessage = '';
  errorMessage = '';
  
  // Available options for dropdowns
  secteurs = [
    'Informatique',
    'Mathématique',
    'Telecommunication',
    'ML',
    'GC',
    'Administration'
  ];
  
  postes = [
    'Professeur',
    'Maître de Conférences',
    'Professeur Associé',
    'Chef de Département',
    'Directeur',
    'Rapporteur',
    'Enseignant',
    'Assistant'
  ];

  constructor(
    private authService: AuthService,
    private utilisateurService: UtilisateurService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.currentUser = user;
        this.loadUserProfile();
      } else {
        this.router.navigate(['/utilisateur']);
      }
    });
  }

  loadUserProfile(): void {
    if (!this.currentUser) return;

    this.isLoading = true;

    // Try to load profile from backend, but fallback to current user data if restricted
    this.utilisateurService.getUserProfile(this.currentUser.email).subscribe({
      next: (profile) => {
        this.profileData = {
          username: profile.username || '',
          poste: profile.poste || '',
          secteur: profile.secteur || '',
          email: profile.email || ''
        };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading profile (using fallback):', error);
        // If profile endpoint fails (due to role restrictions), use current user data
        // This is a temporary workaround until backend is updated to allow all roles
        this.profileData = {
          username: this.currentUser?.username || '',
          poste: this.getDefaultPosteForRole(this.currentUser?.role || ''),
          secteur: 'Informatique', // Default sector
          email: this.currentUser?.email || ''
        };
        this.isLoading = false;
      }
    });
  }

  private getDefaultPosteForRole(role: string): string {
    switch (role.toLowerCase()) {
      case 'enseignant':
        return 'Professeur';
      case 'chef departement':
        return 'Chef de Département';
      case 'rapporteur':
        return 'Rapporteur';
      case 'president jury':
        return 'Président de Jury';
      default:
        return 'Enseignant';
    }
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    this.clearMessages();
    
    if (!this.isEditing) {
      // Reset form when canceling edit
      this.loadUserProfile();
    }
  }

  saveProfile(): void {
    // Profile editing is disabled - users can only change their password
    this.errorMessage = 'La modification du profil n\'est pas autorisée. Vous pouvez uniquement changer votre mot de passe.';

    setTimeout(() => {
      this.errorMessage = '';
    }, 5000);
  }



  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  togglePasswordChange(): void {
    this.isChangingPassword = !this.isChangingPassword;
    this.clearMessages();

    if (!this.isChangingPassword) {
      // Reset password form when canceling
      this.passwordData = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    }
  }

  cancelPasswordChange(): void {
    this.isChangingPassword = false;
    this.passwordData = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
    this.clearMessages();
  }

  isPasswordValid(): boolean {
    return !!(
      this.passwordData.currentPassword &&
      this.passwordData.newPassword &&
      this.passwordData.confirmPassword &&
      this.passwordData.newPassword === this.passwordData.confirmPassword &&
      this.passwordData.newPassword.length >= 6
    );
  }

  changePassword(): void {
    if (!this.isPasswordValid()) {
      this.errorMessage = 'Veuillez remplir tous les champs correctement.';
      return;
    }

    this.isLoading = true;
    this.clearMessages();

    this.utilisateurService.changePassword(this.passwordData).subscribe({
      next: () => {
        this.successMessage = 'Mot de passe changé avec succès!';
        this.isChangingPassword = false;
        this.isLoading = false;

        // Reset password form
        this.passwordData = {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        };

        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      },
      error: (error) => {
        console.error('Error changing password:', error);

        if (error.status === 400) {
          this.errorMessage = 'Mot de passe actuel incorrect.';
        } else if (error.status === 403) {
          this.errorMessage = 'Accès refusé. Veuillez vous reconnecter.';
        } else {
          this.errorMessage = 'Erreur lors du changement de mot de passe. Veuillez réessayer.';
        }

        this.isLoading = false;

        setTimeout(() => {
          this.errorMessage = '';
        }, 7000);
      }
    });
  }

  goBack(): void {
    this.authService.redirectToDashboard();
  }
}

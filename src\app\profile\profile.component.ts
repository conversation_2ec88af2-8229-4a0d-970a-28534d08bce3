import { Component, OnInit } from '@angular/core';
import { AuthService, User } from '../services/auth.service';
import { UtilisateurService } from '../utilisateur/utilisateur.service';
import { Router } from '@angular/router';

export interface ProfileUpdateRequest {
  username: string;
  poste: string;
  secteur: string;
  email: string;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  standalone: false
})
export class ProfileComponent implements OnInit {
  currentUser: User | null = null;
  profileData: ProfileUpdateRequest = {
    username: '',
    poste: '',
    secteur: '',
    email: ''
  };
  
  isEditing = false;
  isLoading = false;
  successMessage = '';
  errorMessage = '';
  
  // Available options for dropdowns
  secteurs = [
    'Informatique',
    'Mathématique',
    'Telecommunication',
    'ML',
    'GC',
    'Administration'
  ];
  
  postes = [
    'Professeur',
    'Maî<PERSON> de Conférences',
    'Professeur <PERSON>',
    'Chef de <PERSON>',
    'Directeur',
    'Rapporteur',
    'Enseignant',
    'Assistant'
  ];

  constructor(
    private authService: AuthService,
    private utilisateurService: UtilisateurService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.currentUser = user;
        this.loadUserProfile();
      } else {
        this.router.navigate(['/utilisateur']);
      }
    });
  }

  loadUserProfile(): void {
    if (!this.currentUser) return;

    this.isLoading = true;

    // Try to load profile from backend, but fallback to current user data if restricted
    this.utilisateurService.getUserProfile(this.currentUser.email).subscribe({
      next: (profile) => {
        this.profileData = {
          username: profile.username || '',
          poste: profile.poste || '',
          secteur: profile.secteur || '',
          email: profile.email || ''
        };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading profile (using fallback):', error);
        // If profile endpoint fails (due to role restrictions), use current user data
        // This is a temporary workaround until backend is updated to allow all roles
        this.profileData = {
          username: this.currentUser?.username || '',
          poste: this.getDefaultPosteForRole(this.currentUser?.role || ''),
          secteur: 'Informatique', // Default sector
          email: this.currentUser?.email || ''
        };
        this.isLoading = false;
      }
    });
  }

  private getDefaultPosteForRole(role: string): string {
    switch (role.toLowerCase()) {
      case 'enseignant':
        return 'Professeur';
      case 'chef departement':
        return 'Chef de Département';
      case 'rapporteur':
        return 'Rapporteur';
      case 'president jury':
        return 'Président de Jury';
      default:
        return 'Enseignant';
    }
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    this.clearMessages();
    
    if (!this.isEditing) {
      // Reset form when canceling edit
      this.loadUserProfile();
    }
  }

  saveProfile(): void {
    if (!this.validateForm()) {
      return;
    }

    this.isLoading = true;
    this.clearMessages();

    this.utilisateurService.updateProfile(this.profileData).subscribe({
      next: (updatedUser) => {
        this.successMessage = 'Profil mis à jour avec succès!';
        this.isEditing = false;
        this.isLoading = false;

        // Update the current user in auth service if username changed
        if (this.currentUser && updatedUser.username !== this.currentUser.username) {
          const updatedCurrentUser: User = {
            ...this.currentUser,
            username: updatedUser.username
          };
          // Update session storage
          sessionStorage.setItem('username', updatedUser.username);
        }

        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      },
      error: (error) => {
        console.error('Error updating profile:', error);

        // Check if it's a role restriction error (403 Forbidden)
        if (error.status === 403) {
          this.errorMessage = 'Mise à jour du profil temporairement restreinte. Contactez l\'administrateur pour modifier votre profil.';
        } else {
          this.errorMessage = 'Erreur lors de la mise à jour du profil. Veuillez réessayer.';
        }

        this.isLoading = false;

        setTimeout(() => {
          this.errorMessage = '';
        }, 7000);
      }
    });
  }

  validateForm(): boolean {
    if (!this.profileData.username.trim()) {
      this.errorMessage = 'Le nom d\'utilisateur est requis.';
      return false;
    }

    if (!this.profileData.email.trim()) {
      this.errorMessage = 'L\'email est requis.';
      return false;
    }

    if (!this.profileData.poste.trim()) {
      this.errorMessage = 'Le poste est requis.';
      return false;
    }

    if (!this.profileData.secteur.trim()) {
      this.errorMessage = 'Le secteur est requis.';
      return false;
    }

    return true;
  }

  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  goBack(): void {
    this.authService.redirectToDashboard();
  }
}

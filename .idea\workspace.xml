<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="4" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ce712155-09cd-488b-be2b-320c609ba9f7" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/src/main/java/com/example/microserviceuser/Controller/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/src/main/java/com/example/microserviceuser/Controller/UserController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2ziwWJixMfaQ1evnbmWxK05u6vr" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.microserviceRapport [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.microserviceRapport [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.microserviceRectification [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.microserviceRectification [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.MicroserviceRapportApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MicroserviceRectificationApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MicroserviceUserApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;youssef&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/-Back&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.annotationProcessors&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.MicroserviceRapportApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="backend" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MicroserviceRapportApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="microserviceRapport" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tn.esprit.microservicerapport.MicroserviceRapportApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MicroserviceRectificationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="microserviceRectification" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tn.esprit.microservicerectification.MicroserviceRectificationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MicroserviceUserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="tpFoyer-17" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.microserviceuser.MicroserviceUserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.MicroserviceRapportApplication" />
      <item itemvalue="Spring Boot.MicroserviceRectificationApplication" />
      <item itemvalue="Spring Boot.MicroserviceUserApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-b114ca120d71-intellij.indexing.shared.core-IU-242.21829.142" />
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-IU-242.21829.142" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ce712155-09cd-488b-be2b-320c609ba9f7" name="Changes" comment="" />
      <created>1752227233451</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752227233451</updated>
      <workItem from="1752227235691" duration="197000" />
      <workItem from="1752227513475" duration="33000" />
      <workItem from="1752227580698" duration="704000" />
      <workItem from="1752325397306" duration="3581000" />
      <workItem from="1752330044841" duration="57000" />
      <workItem from="1752330917042" duration="265000" />
      <workItem from="1752331887989" duration="692000" />
      <workItem from="1752406697754" duration="948000" />
      <workItem from="1752498474022" duration="45000" />
      <workItem from="1752510468271" duration="1069000" />
      <workItem from="1752512889250" duration="5057000" />
      <workItem from="1752531839085" duration="4532000" />
      <workItem from="1752536428610" duration="6000" />
      <workItem from="1752537294447" duration="1088000" />
      <workItem from="1752538428954" duration="379000" />
      <workItem from="1752538992724" duration="213000" />
      <workItem from="1752570995352" duration="3375000" />
      <workItem from="1752617266246" duration="2584000" />
      <workItem from="1752705546057" duration="5166000" />
      <workItem from="1752881101360" duration="2815000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>